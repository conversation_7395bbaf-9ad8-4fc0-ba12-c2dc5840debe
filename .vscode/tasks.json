{
  // See https://go.microsoft.com/fwlink/?LinkId=733558
  // for the documentation about the tasks.json format
  "version": "2.0.0",
  "tasks": [{
    "label": "Dev Api",
    "type": "shell",
    "command": "make",
    "args": ["dev-api"],
    "problemMatcher": []
  }, {
    "label": "Dev Web",
    "type": "shell",
    "command": "make",
    "args": ["dev-web"],
    "problemMatcher": []
  }, {
    "label": "Build Api",
    "type": "shell",
    "command": "make",
    "args": ["build-api"],
    "problemMatcher": []
  }, {
    "label": "Build Web",
    "type": "shell",
    "command": "make",
    "args": ["build-web"],
    "problemMatcher": []
  }]
}
